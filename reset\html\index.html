<!doctype html>
<html lang="vi">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>Đặt lại mật khẩu</title>
    <style>
        body {
            font-family: system-ui, <PERSON><PERSON>;
            margin: 40px;
            max-width: 520px
        }

        input,
        button {
            padding: 10px;
            margin: 6px 0;
            width: 100%
        }

        .card {
            border: 1px solid #ddd;
            border-radius: 12px;
            padding: 20px
        }
    </style>
    <!-- Firebase JS SDK (version bất kỳ 10.x) -->
    <script src="https://www.gstatic.com/firebasejs/10.12.2/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.12.2/firebase-auth-compat.js"></script>
</head>

<body>
    <h2>Đặt lại mật khẩu</h2>
    <div id="box" class="card"><PERSON><PERSON> kiểm tra mã…</div>

    <script>
        // TODO: THAY bằng cấu hình Web của dự án bạn (Firebase Console → Project settings → General → Web app)
        const firebaseConfig = {
            apiKey: "YOUR_API_KEY",
            authDomain: "khuyetcong-a4a7a.firebaseapp.com",
            projectId: "khuyetcong-a4a7a",
            appId: "YOUR_APP_ID",
        };
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();

        const params = new URLSearchParams(location.search);
        const mode = params.get('mode');
        const oobCode = params.get('oobCode'); // Firebase viết hoa “oobCode”
        const continueUrl = params.get('continueUrl') || '/';

        const box = document.getElementById('box');

        if (mode === 'resetPassword' && oobCode) {
            auth.verifyPasswordResetCode(oobCode).then(email => {
                // Hiển thị form đặt mật khẩu mới
                box.innerHTML = `
          <p>Tài khoản: <b>${email}</b></p>
          <input id="pw1" type="password" placeholder="Mật khẩu mới (≥ 6 ký tự)">
          <input id="pw2" type="password" placeholder="Nhập lại mật khẩu mới">
          <button id="ok">Đổi mật khẩu</button>
          <div id="msg"></div>
        `;
                document.getElementById('ok').onclick = async () => {
                    const p1 = document.getElementById('pw1').value;
                    const p2 = document.getElementById('pw2').value;
                    const msg = document.getElementById('msg');
                    if (p1.length < 6) { msg.textContent = 'Mật khẩu tối thiểu 6 ký tự'; return; }
                    if (p1 !== p2) { msg.textContent = 'Mật khẩu nhập lại không khớp'; return; }
                    try {
                        await auth.confirmPasswordReset(oobCode, p1);
                        msg.textContent = 'Đổi mật khẩu thành công. Đang chuyển hướng…';
                        setTimeout(() => location.href = continueUrl, 1200);
                    } catch (e) { msg.textContent = 'Lỗi: ' + (e.message || e); }
                };
            }).catch(err => {
                box.textContent = 'Mã đặt lại không hợp lệ hoặc đã hết hạn. Vui lòng yêu cầu lại email.';
            });
        } else {
            box.textContent = 'Thiếu tham số. Hãy mở từ đường link trong email.';
        }
    </script>
</body>

</html>