// send_reset.js
import admin from 'firebase-admin';
import nodemailer from 'nodemailer';
import fs from 'fs';

// 1. Khởi tạo Firebase Admin SDK
const serviceAccount = JSON.parse(fs.readFileSync('./serviceAccountKey.json', 'utf8'));
admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
});

// 2. Cấu hình SMTP cho AFOV IT Dev
const transporter = nodemailer.createTransport({
    host: 'smtp.office365.com',
    port: 587,
    secure: false, // TLS
    auth: {
        user: '<EMAIL>', // tài khoản công ty
        pass: 'Longan1234567,' // liên hệ IT lấy app password nếu bật MFA
    }
});

// 3. Hàm gửi email
async function sendResetEmail(userEmail) {
    try {
        // Tạo link reset từ Firebase
        const resetLink = await admin.auth().generatePasswordResetLink(userEmail);

        // Gửi email qua SMTP
        await transporter.sendMail({
            from: '"AFOV IT Dev" <<EMAIL>>',
            to: userEmail,
            subject: 'Yêu cầu đặt lại mật khẩu',
            html: `
        <p>Xin chào,</p>
        <p>Bạn vừa yêu cầu đặt lại mật khẩu cho tài khoản của mình.</p>
        <p>Nhấn vào link bên dưới để đặt lại:</p>
        <a href="${resetLink}">${resetLink}</a>
        <p>Link này sẽ hết hạn sau một thời gian ngắn.</p>
      `
        });

        console.log(`✅ Email reset đã gửi tới: ${userEmail}`);
    } catch (error) {
        console.error(`❌ Lỗi gửi email tới ${userEmail}:`, error);
    }
}

// 4. Chạy thử
const emailToReset = process.argv[2];
if (!emailToReset) {
    console.error('Vui lòng nhập email cần reset. Ví dụ: node send_reset.js <EMAIL>');
    process.exit(1);
}
sendResetEmail(emailToReset);
